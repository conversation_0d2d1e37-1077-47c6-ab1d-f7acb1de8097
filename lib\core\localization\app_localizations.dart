import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppLocalizations {
  final Locale locale;
  late Map<String, String> _localizedStrings;

  AppLocalizations(this.locale);

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  Future<bool> load() async {
    String jsonString = await rootBundle
        .loadString('assets/lang/${locale.languageCode}.json');
    Map<String, dynamic> jsonMap = json.decode(jsonString);

    _localizedStrings = jsonMap.map((key, value) {
      return MapEntry(key, value.toString());
    });

    return true;
  }

  String translate(String key) {
    return _localizedStrings[key] ?? key;
  }

  // Convenience getters for commonly used strings
  String get appTitle => translate('app_title');
  String get login => translate('login');
  String get username => translate('username');
  String get password => translate('password');
  String get usernameHint => translate('username_hint');
  String get passwordHint => translate('password_hint');
  String get loginButton => translate('login_button');
  String get loginSuccess => translate('login_success');
  String get loginFailed => translate('login_failed');
  String get invalidCredentials => translate('invalid_credentials');
  String get networkError => translate('network_error');
  String get serverError => translate('server_error');
  String get home => translate('home');
  String get welcome => translate('welcome');
  String get logout => translate('logout');
  String get profile => translate('profile');
  String get settings => translate('settings');
  String get loading => translate('loading');
  String get error => translate('error');
  String get retry => translate('retry');
  String get cancel => translate('cancel');
  String get ok => translate('ok');
  String get yes => translate('yes');
  String get no => translate('no');
  String get save => translate('save');
  String get delete => translate('delete');
  String get edit => translate('edit');
  String get add => translate('add');
  String get search => translate('search');
  String get filter => translate('filter');
  String get sort => translate('sort');
  String get refresh => translate('refresh');
  String get back => translate('back');
  String get next => translate('next');
  String get previous => translate('previous');
  String get done => translate('done');
  String get close => translate('close');
  String get open => translate('open');
  String get view => translate('view');
  String get share => translate('share');
  String get copy => translate('copy');
  String get paste => translate('paste');
  String get cut => translate('cut');
  String get selectAll => translate('select_all');
  String get clear => translate('clear');
  String get reset => translate('reset');
  String get submit => translate('submit');
  String get send => translate('send');
  String get receive => translate('receive');
  String get upload => translate('upload');
  String get download => translate('download');
  String get import => translate('import');
  String get export => translate('export');
  String get print => translate('print');
  String get help => translate('help');
  String get about => translate('about');
  String get version => translate('version');
  String get update => translate('update');
  String get upgrade => translate('upgrade');
  String get install => translate('install');
  String get uninstall => translate('uninstall');
  String get enable => translate('enable');
  String get disable => translate('disable');
  String get activate => translate('activate');
  String get deactivate => translate('deactivate');
  String get connect => translate('connect');
  String get disconnect => translate('disconnect');
  String get sync => translate('sync');
  String get backup => translate('backup');
  String get restore => translate('restore');
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['en', 'ar'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    AppLocalizations localizations = AppLocalizations(locale);
    await localizations.load();
    return localizations;
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
